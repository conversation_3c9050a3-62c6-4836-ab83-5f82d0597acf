import random
from datetime import date, timedelta
from django.core.management.base import BaseCommand
from fuel.models import Driver, FuelConsumption

class Command(BaseCommand):
    help = 'Simulate fuel consumption with separate gas slips for multiple trips per day using a fixed price of ₱56.50 per liter'

    def handle(self, *args, **kwargs):
        # Clear all existing FuelConsumption records to avoid unique constraint errors
        FuelConsumption.objects.all().delete()
        # Set up driver shifts.
        driver_shifts = {
            "Ambulance L300": [
                ("Antonio Tenebro", "Ambulance L300"),
                ("<PERSON>", "Ambulance L300"),
                ("Rey Berjame", "Ambulance L300")
            ],
            "Ambulance Province": [
                ("<PERSON> Daryl Ginggo", "Ambulance Province"),
                ("Jeweriel Sulatorio", "Ambulance Province")
            ],
            "Ambulance DOH": [
                ("Crisbanie Jay Paran", "Ambulance DOH"),
                ("Aldren Urot", "Ambulance DOH")
            ],
            "Backhoe": [
                ("<PERSON>", "Backhoe"),
                ("<PERSON>", "Backhoe")  # Same driver for both shifts
            ],
            "Dumptruck": [
                ("Raymond Hangcan", "Dumptruck"),
                ("<PERSON> Hangcan", "Dumptruck")  # Same driver for both shifts
            ]
        }

        drivers_by_vehicle = {}
        for vehicle, driver_list in driver_shifts.items():
            drivers_by_vehicle[vehicle] = []
            for name, vehicle_type in driver_list:
                driver, created = Driver.objects.get_or_create(
                    name=name,
                    defaults={'vehicle': vehicle_type}
                )
                if not created and driver.vehicle != vehicle_type:
                    driver.vehicle = vehicle_type
                    driver.save()
                drivers_by_vehicle[vehicle].append(driver)

        # Fixed price per liter.
        PRICE_PER_LITER = 57.00

        # Allocation constants
        TOTAL_BUDGET = 312550.0  # Updated budget
        TOTAL_LITERS = TOTAL_BUDGET / PRICE_PER_LITER

        # Destination-based fuel budget (in pesos) - for ambulances only
        # Local area is excluded as it's reserved for heavy equipment
        ambulance_destinations = [
            ('dipolog', 2500),
            ('cagayan', 5000),
            ('margosatubig', 2000),
            ('pagadian_city', 1000),
            ('ozamiz_city', 1500),
            ('zamboanga_city', 5000),
            ('ipil', 3500),
            ('sindangan', 1500),
        ]

        # Define Ozamiz City trip details for final utilization
        OZAMIZ_TRIP_COST = 1350  # Cost for the final Ozamiz City trip
        OZAMIZ_LITERS = OZAMIZ_TRIP_COST / PRICE_PER_LITER  # Calculate liters needed (23.89L)
        
        # Create a dictionary for easy lookup
        ambulance_destinations_dict = dict(ambulance_destinations)

        # Simulation period: February 5, 2025 to March 27, 2025.
        start_date = date(2025, 2, 5)
        end_date = date(2025, 3, 27)
        num_days = (end_date - start_date).days + 1

        remaining_fuel = TOTAL_LITERS

        # Dictionary to track trip_number for each (driver.id, date)
        trip_numbers = {}

        # Reference number counter
        reference_number = 1
        
        # Track total fuel consumed
        total_fuel_consumed = 0
        
        # Track if we've used all special heavy equipment purposes
        special_purposes_used = 0

        # Pre-select four unique dates for heavy equipment special purposes, spread out over the simulation period
        special_purposes = [
            'reroute river manlabay',
            'landslide dapiwak',
            'landslide san vicente',
            'landslide senote'
        ]
        special_dates = []
        if num_days >= 4:
            step = num_days // 4
            for i in range(4):
                special_dates.append(start_date + timedelta(i * step))
        else:
            # fallback: just use the first 4 days
            for i in range(4):
                special_dates.append(start_date + timedelta(i))
        special_purpose_date_map = dict(zip(special_dates, special_purposes))

        # Prepare ambulance driver rotation per vehicle
        ambulance_driver_rotations = {}
        for vehicle, driver_list in drivers_by_vehicle.items():
            if vehicle.startswith('Ambulance'):
                ambulance_driver_rotations[vehicle] = {
                    'drivers': driver_list,
                    'index': 0
                }

        # Main simulation loop - continue until we've used all fuel or reached max date
        current_date = start_date
        day_counter = 0
        max_days = 1000  # Safety limit to prevent infinite loops
        
        while remaining_fuel > 0.1 and day_counter < max_days:  # 0.1L threshold to account for floating point
            if day_counter > 0:
                current_date += timedelta(days=1)
                
            # Process each vehicle type
            for vehicle, driver_list in drivers_by_vehicle.items():
                # Skip processing if we're out of fuel
                if remaining_fuel < 0.1:
                    break
                    
                # For ambulances, rotate drivers
                if vehicle.startswith('Ambulance'):
                    rotation = ambulance_driver_rotations[vehicle]
                    active_driver = rotation['drivers'][rotation['index']]
                    rotation['index'] = (rotation['index'] + 1) % len(rotation['drivers'])
                    
                    # Determine number of trips for this driver today
                    trips_today = random.randint(2, 3) if current_date.weekday() == 6 else random.randint(1, 2)
                    
                    for _ in range(trips_today):
                        # Check if we have enough fuel for at least one trip
                        if remaining_fuel < 10:  # Minimum fuel needed for a short trip
                            break
                            
                        # Get or initialize trip number for this driver and date
                        key = (active_driver.id, current_date)
                        trip_number = trip_numbers.get(key, 1)
                        
                        # Select purpose and destination for this trip
                        selected_purpose = 'Transport Patient'  # Default purpose for ambulances
                        
                        # Filter destinations that fit in remaining fuel
                        possible_destinations = []
                        for dest, cost in ambulance_destinations:
                            required_liters = cost / PRICE_PER_LITER
                            if required_liters <= remaining_fuel:
                                possible_destinations.append((dest, cost))
                        
                        # If we have possible destinations, choose one randomly
                        if possible_destinations:
                            selected_destination, trip_cost = random.choice(possible_destinations)
                            required_liters = trip_cost / PRICE_PER_LITER
                        # If no destination fits but we have enough for minimum trip
                        elif remaining_fuel >= 10:
                            selected_destination = random.choice(ambulance_destinations)[0]  # Choose any destination
                            trip_cost = 10 * PRICE_PER_LITER
                            required_liters = 10
                        else:
                            continue  # Skip this trip if we can't even do the minimum
                            
                        try:
                            FuelConsumption.objects.create(
                                driver=active_driver,
                                reference_number=reference_number,
                                date=current_date,
                                trip_number=trip_number,
                                number_of_trips=1,
                                purpose=selected_purpose,
                                destination=selected_destination,
                                total_liters=required_liters,
                                cost=trip_cost,
                                vehicle=active_driver.vehicle
                            )
                            remaining_fuel -= required_liters
                            total_fuel_consumed += required_liters
                            reference_number += 1
                            trip_numbers[key] = trip_number + 1
                            
                        except ValidationError as ve:
                            self.stdout.write(self.style.WARNING(f"Validation error: {ve.messages}"))
                
                # For heavy equipment, only schedule on special dates and if we haven't used all special purposes
                elif vehicle in ["Backhoe", "Dumptruck"] and special_purposes_used < 4:
                    if current_date in special_purpose_date_map:
                        active_driver = driver_list[0]  # Only one driver per heavy equipment
                        
                        # Get or initialize trip number for this driver and date
                        key = (active_driver.id, current_date)
                        trip_number = trip_numbers.get(key, 1)
                        
                        # Use the special purpose for this date
                        selected_purpose = special_purpose_date_map[current_date]
                        selected_destination = 'local'  # Heavy equipment only does local trips
                        
                        # Calculate fuel needed (400L per trip for heavy equipment)
                        required_liters = 400.0
                        trip_cost = required_liters * PRICE_PER_LITER
                        
                        # Only proceed if we have enough fuel for this heavy equipment trip
                        if required_liters <= remaining_fuel:
                            try:
                                FuelConsumption.objects.create(
                                    driver=active_driver,
                                    reference_number=reference_number,
                                    date=current_date,
                                    trip_number=trip_number,
                                    number_of_trips=1,
                                    purpose=selected_purpose,
                                    destination=selected_destination,
                                    total_liters=required_liters,
                                    cost=trip_cost,
                                    vehicle=active_driver.vehicle
                                )
                                remaining_fuel -= required_liters
                                total_fuel_consumed += required_liters
                                reference_number += 1
                                trip_numbers[key] = trip_number + 1
                                special_purposes_used += 1
                                
                            except ValidationError as ve:
                                self.stdout.write(self.style.WARNING(f"Validation error: {ve.messages}"))
            
            day_counter += 1
            
            # If we've reached the end date but still have fuel, continue to the next day
            if current_date >= end_date and remaining_fuel > 0.1:
                self.stdout.write(self.style.WARNING(f"Extended simulation beyond {end_date} to utilize remaining fuel"))
                end_date += timedelta(days=1)

        # Print summary
        count = FuelConsumption.objects.count()
        total_cost = total_fuel_consumed * PRICE_PER_LITER
        utilization = (total_fuel_consumed / TOTAL_LITERS) * 100
        
        self.stdout.write(
            self.style.SUCCESS(f'Simulation completed on {current_date}')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Created {count} FuelConsumption records.')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Total fuel consumed: {total_fuel_consumed:.2f}L')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Total cost: ₱{total_cost:,.2f}')
        )
        # Calculate remaining budget and check for final Ozamiz trip
        remaining_budget = remaining_fuel * PRICE_PER_LITER
        OZAMIZ_COST = 1000.00  # Fixed cost for Ozamiz trip
        OZAMIZ_LITERS = round(OZAMIZ_COST / PRICE_PER_LITER, 2)
        
        self.stdout.write(f'\nChecking final trip options...')
        self.stdout.write(f'Remaining budget: ₱{remaining_budget:,.2f} ({remaining_fuel:.2f}L)')
        
        # If we have enough for an Ozamiz trip (₱1000) and at least 17.7L (₱1000)
        if remaining_budget >= OZAMIZ_COST and remaining_fuel >= 17.7:
            try:
                # Get the last reference number
                last_ref = FuelConsumption.objects.order_by('-reference_number').first()
                next_ref = (last_ref.reference_number + 1) if last_ref else 1
                
                # Get any available driver (prefer ambulance drivers)
                driver = Driver.objects.filter(name__in=[
                    'Jorge T. Cabilin', 'Marlon M. Dacula', 'Reynaldo M. Dacula',
                    'Reynaldo B. Dacula', 'Jayson A. Dacula', 'Ernesto B. Dacula'
                ]).first()
                
                if not driver:
                    driver = Driver.objects.first()
                    if not driver:
                        raise Exception("No drivers found in the database")
                
                # Create the final Ozamiz trip
                final_trip = FuelConsumption.objects.create(
                    date=current_date,
                    driver=driver,
                    vehicle='Ambulance L300',  # Default ambulance
                    destination='ozamiz',
                    purpose='Transport Patient',
                    total_liters=OZAMIZ_LITERS,
                    cost=OZAMIZ_COST,
                    reference_number=next_ref
                )
                
                # Update totals
                total_fuel_consumed += OZAMIZ_LITERS
                total_cost += OZAMIZ_COST
                remaining_fuel = max(0, round(remaining_fuel - OZAMIZ_LITERS, 2))
                
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Added Ozamiz City trip for ₱{OZAMIZ_COST:,.2f} ({OZAMIZ_LITERS:.2f}L)')
                )
                self.stdout.write(
                    self.style.SUCCESS(f'   Remaining after Ozamiz trip: {remaining_fuel:.2f}L (₱{remaining_fuel * PRICE_PER_LITER:,.2f})')
                )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Failed to add final Ozamiz trip: {str(e)}')
                )
        # If we have some fuel left but not enough for Ozamiz
        elif remaining_fuel > 0.1:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Not enough for Ozamiz trip (needs ₱{OZAMIZ_COST:,.2f} / {OZAMIZ_LITERS:.2f}L)')
            )
            
            # Add a local trip with remaining fuel
            try:
                # Get the last reference number
                last_ref = FuelConsumption.objects.order_by('-reference_number').first()
                next_ref = (last_ref.reference_number + 1) if last_ref else 1
                
                # Get any available driver
                driver = Driver.objects.first()
                if not driver:
                    raise Exception("No drivers found in the database")
                
                # Create a local trip with remaining fuel
                local_trip = FuelConsumption.objects.create(
                    date=current_date,
                    driver=driver,
                    vehicle='Ambulance L300',
                    destination='local',
                    purpose='Local Transport',
                    total_liters=round(remaining_fuel, 2),
                    cost=round(remaining_fuel * PRICE_PER_LITER, 2),
                    reference_number=next_ref
                )
                
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Added local trip for remaining ₱{remaining_budget:,.2f} ({remaining_fuel:.2f}L)')
                )
                remaining_fuel = 0
                total_fuel_consumed += remaining_fuel
                total_cost += remaining_budget
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Failed to add local trip: {str(e)}')
                )
        else:
            self.stdout.write(
                self.style.SUCCESS('Budget fully utilized with no remaining fuel')
            )
        
        self.stdout.write(
            self.style.SUCCESS(f'Budget utilization: {(total_cost / (TOTAL_LITERS * PRICE_PER_LITER)) * 100:.2f}%')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Remaining fuel: {remaining_fuel:.2f}L (₱{remaining_fuel * PRICE_PER_LITER:,.2f})')
        )